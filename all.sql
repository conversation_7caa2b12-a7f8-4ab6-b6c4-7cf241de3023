set hive.mapred.mode=nonstrict;
-- create table if not exists ad_algo_f.search_ads_rank_model_montinor_1d (
--      pkey   string 
--     ,gkey   string
--     ,skey   string
--     ,desc   string
--     ,score_sum  double
--     ,label_sum  double
--     ,admit_sum  double
--     ,prio       double
--     ,post       double
--     ,pcoc       double
--     ,logloss    double
--     ,auc        double
--     ,bid_boost_avg  double
--     ,client_bid_avg double
--     ,system_bid_avg double
--     ,bid_ratio_avg  double
--     ,utr_calc       double
--     ,utr_real       double
--     ,uvr_calc       double
--     ,uvr_real       double
--     ,ecpc_sum       double
--     ,cpm            double
--     ,price          double
--     ,cost           double
--     ,target_cost    double
-- ) partitioned by ( 
--    p_date   string 
-- );



with sla_0 as (
----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
----------------------------------------------------------------------     垂搜-短视频-订单    -------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
select *
    ,'垂搜/短视频/订单'                                 as pkey 
    ,concat(
        'onwer:wangzhiping03;',
        'time:20250729;' 
        'version:(1.0) Initialization;'
        'desc: ctr=P(下发->点击), cvr=P(点击->转化)')  as desc 
    -- STR
    ,feed_ctr                 as s_str   -- sctr score
    ,if(item_clk>0, 1.0, 0.0) as l_str   -- sctr label    
    ,if(delivery>0, 1.0, 0.0) as a_str   -- sctr admit

    -- CTR
    ,1.0                      as s_ctr   -- ctr score
    ,1.0                      as l_ctr   -- ctr label
    ,0.0                      as a_ctr   -- ctr admit      

    -- CVR
    ,unify_cvr                as s_cvr   -- cvr score
    ,if(orders>0, 1.0, 0.0)   as l_cvr   -- cvr label
    ,if(item_clk>0, 1.0, 0.0) as a_cvr   -- cvr admit  

    -- DEEP_CVR
    ,1.0                      as s_dvr   -- dcvr score
    ,1.0                      as l_dvr   -- dcvr label
    ,0.0                      as a_dvr   -- dcvr admit

    -- LTV
    ,1.0                      as s_ltv   -- ltv score
    ,1.0                      as l_ltv   -- ltv label
    ,0.0                      as a_ltv   -- ltv admit

    -- UNIFY CTR   
    ,feed_ctr                 as s_utr -- 理论 unify ctr score
    ,if(item_clk>0, 1.0, 0.0) as l_utr -- 理论 unify ctr label   
    ,if(delivery>0, 1.0, 0.0) as a_utr -- 理论 unify ctr admit

     -- UNIFY CVR  
    ,unify_cvr                as s_uvr -- 理论 unify cvr score 
    ,if(orders>0, 1.0, 0.0)   as l_uvr -- 理论 unify cvr label
    ,if(item_clk>0, 1.0, 0.0) as a_uvr -- 理论 unify cvr admit

     -- UNIFY LTV
    ,1.0                                as s_ult  -- 理论 unify ltv score
    ,0.0                                as l_ult  -- 理论 unify ltv label
    ,0.0                                as a_ult  -- 理论 unify ltv admit 

     -- FIANL LABEL
    ,if(orders>0, 1.0, 0.0)  as conversion_num  -- 转化数
    ,unit_id                 as unit_key   

     -- BID
    ,cpa_bid                  as client_bid   -- 客户系统
    ,auto_cpa_bid             as system_bid   -- 系统出价 

from ad_algo_f.ad_search_delivery_ascribed_1d
where p_date = '{{ ds_nodash }}'
-- 筛选区
and sub_page_id in (100013954, 100013955, 100019265, 100019263, 100019264, 100019266) -- 垂搜
and item_type  = 'ITEM_PHOTO' -- 短视频
and ocpc_action_type = 'EVENT_ORDER_PAIED' -- 订单支付



----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
----------------------------------------------------------------------     综搜-短视频-APP-首R    -------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
union all
select *
    ,'综搜/短视频/APP首日ROI'                         as pkey 
    ,concat(
        'onwer:dangpingbo;',
        'time:20250729;' 
        'version:(1.0) Initialization;'
        'desc: sctr=P(下发->封面曝光), ctr(封面曝光->素材曝光), cvr=P(素材曝光->激活), dvr=P(激活->付费), ltv=P(付费->LTV)')  as desc 
    -- STR
    ,if(sub_page_id in (10014001, 11014001), server_client_show_rate, server_show_ctr)   as s_str   -- sctr score
    ,case 
        when sub_page_id in (10014001, 11014001) and style = 'APP_DOWNLOAD_TYPE' then if(item_imp >0, 1, 0) -- 外流大卡
        when sub_page_id in (10014001, 11014001) and style = 'DEFAULT_FEED_TYPE' then if(photo_imp>0, 1, 0) -- 外流普通
        when sub_page_id in (100018489, 100018490)                               then if(item_imp >0, 1, 0) -- 内流  
        end                   as l_str   -- sctr label      
    ,if(delivery>0, 1.0, 0.0) as a_str   -- sctr admit

    -- CTR
    ,feed_ctr                 as s_ctr   -- ctr score
    ,if(photo_clk>0, 1, 0)    as l_ctr   -- ctr label
    ,if(sub_page_id in (10014001, 11014001) and style = 'DEFAULT_FEED_TYPE' and photo_imp>0, 1, 0) as a_ctr   -- ctr admit      

    -- CVR
    ,conversion_rate             as s_cvr   -- cvr score
    ,if(conversion>0, 1.0, 0.0)  as l_cvr   -- cvr label
    ,if(item_imp>0, 1.0, 0.0)    as a_cvr   -- cvr admit        

    -- DEEP_CVR
    ,purchase_rate              as s_dvr   -- dcvr score
    ,if(purchase>0,   1.0, 0.0)  as l_dvr   -- dcvr label
    ,if(conversion>0, 1.0, 0.0)  as a_dvr   -- dcvr admit
    
    -- LTV
    ,conv_1d_ltv              as s_ltv   -- ltv score
    ,ltv_max                  as l_ltv   -- ltv label
    ,if(purchase>0, 1.0, 0.0) as a_ltv   -- ltv admit

    -- UNIFY CTR  
    ,case 
        when sub_page_id in (10014001, 11014001) and style = 'APP_DOWNLOAD_TYPE' then server_client_show_rate            -- 外流大卡
        when sub_page_id in (10014001, 11014001) and style = 'DEFAULT_FEED_TYPE' then server_client_show_rate * feed_ctr -- 外流普通
        when sub_page_id in (100018489, 100018490)                               then server_show_ctr                    -- 内流  
        end                   as s_utr -- 理论 unify ctr score
    ,if(item_imp>0, 1.0, 0.0) as l_utr -- 理论 unify ctr label   
    ,if(delivery>0, 1.0, 0.0) as a_utr -- 理论 unify ctr admit

     -- UNIFY CVR  
    ,conversion_rate * purchase_rate    as s_uvr -- 理论 unify cvr score 
    ,if(purchase>0, 1.0, 0.0)           as l_uvr -- 理论 unify cvr label
    ,if(item_imp>0, 1.0, 0.0)           as a_uvr -- 理论 unify cvr admit

     -- UNIFY LTV
    ,conv_1d_ltv                        as s_ult  -- 理论 unify ltv score
    ,ltv_max                            as l_ult  -- 理论 unify ltv label  
    ,if(purchase>0, 1.0, 0.0)           as a_ult  -- 理论 unify ltv admit  

     -- FIANL label
    ,ltv_max                      as conversion_num  -- 转化数
    ,unit_id                      as unit_key   

     -- BID
    ,1.0/roi_ratio                as client_bid   -- 客户系统
    ,1.0/auto_roi_ratio           as system_bid   -- 系统出价
    
from ad_algo_f.ad_search_delivery_ascribed_1d
where p_date = '{{ ds_nodash }}'
-- 筛选区
and sub_page_id in (10014001, 11014001, 100018489, 100018490) -- 综搜
and item_type  = 'ITEM_PHOTO' -- 短视频
and ocpc_action_type = 'AD_ROAS' -- 订单支付
and campaign_type = 'APP'
and style in ('DEFAULT_FEED_TYPE', 'APP_DOWNLOAD_TYPE')




),


----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 二次加工
sla_1 as (
select *
     -- E2E 
    ,s_utr * s_uvr * s_ult    as s_e2e -- 理论 端到端 score
    ,conversion_num           as l_e2e -- 理论 端到端 label   
    ,if(delivery>0, 1.0, 0.0) as a_e2e -- 理论 端到端 admit

     -- AUCTION
    ,s_utr * s_uvr * s_ult  * system_bid  as s_auc -- 理论 auction score
    ,conversion_num         * client_bid  as l_auc -- 理论 auction label   
    ,if(delivery>0, 1.0, 0.0)             as a_auc -- 理论 auction admit

     -- ECPC
    ,s_utr * s_uvr  * s_ult  * system_bid * bid_boost  as s_epc  -- 理论 ecpc score
    ,conversion_num * client_bid                       as l_epc  -- 理论 ecpc label   
    ,if(delivery>0, 1.0, 0.0)                          as a_epc  -- 理论 ecpc admit 
     
     -- TARGET COST
    ,conversion_num * client_bid  as target_cost  -- 预期花费  
from sla_0
),


-- 下钻维度拆分
groups_1d as (
      select *, 'all'    as gkey from sla_1
union select *, 'strong' as gkey from sla_1 where sub_page_id in (10014001,  11014001) and style not in ('DEFAUTL_FEED_TYPE')
union select *, 'feed'   as gkey from sla_1 where sub_page_id in (10014001,  11014001) and style     in ('DEFAULT_FEED_TYPE')
union select *, 'inner'  as gkey from sla_1 where sub_page_id in (100018489, 100018490)
union select *, 'goods'  as gkey from sla_1 where sub_page_id in (100013954, 100013955, 100019265, 100019263, 100019264, 100019266)
union select *, 'live'   as gkey from sla_1 where sub_page_id in (100013703, 100013704)
union select *, 'photo'  as gkey from sla_1 where sub_page_id in (100024814, 100024815)
),


-- 模型拆分
-- 顺序：pkey, gkey, skey, predict, label(in admit space)
-- 过滤：admit = 1
models_1d as (
-- baisc predictions 
      select pkey, gkey, 'ALL' as skey, 0 as s, 1 as l, 1 as r  from groups_1d group by pkey, gkey                         
union select pkey, gkey, 'STR' as skey, s_str as s, l_str as l, row_number() over (partition by pkey, gkey order by s_str asc) as r from groups_1d where a_str = 1 
union select pkey, gkey, 'CTR' as skey, s_ctr as s, l_ctr as l, row_number() over (partition by pkey, gkey order by s_ctr asc) as r from groups_1d where a_ctr = 1
union select pkey, gkey, 'CVR' as skey, s_cvr as s, l_cvr as l, row_number() over (partition by pkey, gkey order by s_cvr asc) as r from groups_1d where a_cvr = 1
union select pkey, gkey, 'DVR' as skey, s_dvr as s, l_dvr as l, row_number() over (partition by pkey, gkey order by s_dvr asc) as r from groups_1d where a_dvr = 1
union select pkey, gkey, 'LTV' as skey, s_ltv as s, l_ltv as l, row_number() over (partition by pkey, gkey order by s_ltv asc) as r from groups_1d where a_ltv = 1
union select pkey, gkey, 'UTR' as skey, s_utr as s, l_utr as l, row_number() over (partition by pkey, gkey order by s_utr asc) as r from groups_1d where a_utr = 1
union select pkey, gkey, 'UVR' as skey, s_uvr as s, l_uvr as l, row_number() over (partition by pkey, gkey order by s_uvr asc) as r from groups_1d where a_uvr = 1
union select pkey, gkey, 'E2E' as skey, s_e2e as s, l_e2e as l, row_number() over (partition by pkey, gkey order by s_e2e asc) as r from groups_1d where a_e2e = 1
union select pkey, gkey, 'AUC' as skey, s_auc as s, l_auc as l, row_number() over (partition by pkey, gkey order by s_auc asc) as r from groups_1d where a_auc = 1
union select pkey, gkey, 'EPC' as skey, s_epc as s, l_epc as l, row_number() over (partition by pkey, gkey order by s_epc asc) as r from groups_1d where a_epc = 1
)




-- 指标计算
insert overwrite table ad_algo_f.search_ads_rank_model_montinor_1d partition(p_date = '{{ ds_nodash }}')
select 
       coalesce(m.pkey, s.pkey)  as pkey
      ,coalesce(m.gkey, s.gkey)  as gkey  
      ,coalesce(m.skey, s.skey)  as skey
      ,desc
      ,score_sum, label_sum, admit_sum, prio, post, pcoc, logloss, auc
      ,bid_boost_avg, client_bid_avg, system_bid_avg, bid_ratio_avg
      ,utr_calc, utr_real, uvr_calc, uvr_real, ecpc_sum, cpm, price
      ,cost, target_cost
      --,conclude1_cnt, conclude2_cnt, unit_cnt
from (
    select pkey, gkey, skey
        -- model montinor  
        ,sum(s) as score_sum
        ,sum(l) as label_sum
        ,sum(1) as admit_sum
        ,avg(s) as prio
        ,avg(l) as post     
        ,sum(s) / sum(l) as pcoc  
        ,sum(l * ln(s) + (1-l) * ln(1-s)) / sum(1) as logloss
        ,(sum(if(l>0,r,0)) - (sum(l) * (sum(l) + 1) )/2.0) / (sum(l) * sum(1.0-l)) as auc
    from models_1d
    group by  pkey, gkey, skey
) m 
full outer join (
    select 
       pkey, gkey, 'ALL' as skey
      ,max(desc)                    as desc
       -- strategy montinor
      ,avg(bid_boost)               as bid_boost_avg 
      ,avg(client_bid)              as client_bid_avg
      ,avg(system_bid)              as system_bid_avg
      ,avg(system_bid / client_bid) as bid_ratio_avg  

       -- biz montinor
      ,sum(s_utr)                   as utr_calc
      ,sum(unify_ctr)               as utr_real
      ,sum(s_uvr)                   as uvr_calc
      ,sum(unify_cvr)               as uvr_real 
      ,sum(s_epc)                   as ecpc_sum 
      ,sum(cpm)                     as cpm 
      ,sum(price)                   as price
      ,sum(cost)                    as cost
      ,sum(target_cost)             as target_cost
    from groups_1d
    group by  pkey, gkey
) s on m.pkey = s.pkey and m.gkey = s.gkey and m.skey = s.skey
-- full outer join (
--     -- 达成率
--     select pkey, gkey, skey 
--            sum(if(pcoc > 0.9 and pcoc < 1.2, 1, 0))                         as  conclude1_cnt
--            sum(if(pcoc > 0.9 and pcoc < 1.2 and conversion_num > 10, 1, 0)) as  conclude2_cnt
--            sum(1)                                                           as  unit_cnt
--     from (
--         select pkey, gkey, skey, unit_key, sum(s) / sum(l) as pcoc,  sum(l) as conversion_num, sum(cost) as cost
--         from models_1d
--         group by pkey, gkey, skey, unit_key
--     ) x 
--     group by pkey, gkey, skey
-- ) p on m.pkey = p.pkey and m.gkey = p.gkey and m.skey = p.skey
;