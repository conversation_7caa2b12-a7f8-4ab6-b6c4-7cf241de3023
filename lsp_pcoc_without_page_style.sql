with lps_info as (
       select p_date
              , llsid
              , creative_id
              , ocpc_action_type
              , if(max(sub_page_id) in (100018489,100018490), 'inner', 'outer') as page_type
              , if(max(get_json_object(online_join_params, '$.searchRealStyleType')) = 'DEFAULT_FEED_TYPE', 'normal', 'bigcard') as style
              , case when max(lookupCurrentGroup('w_n_kuaishou_apps_did_4532', 0, device_id)) in ('base', 'exp1') then 'base'
                     when max(lookupCurrentGroup('w_n_kuaishou_apps_did_4532', 0, device_id)) in ('exp2', 'exp3') then 'group1'
                     when max(lookupCurrentGroup('w_n_kuaishou_apps_did_4532', 0, device_id)) in ('exp4', 'exp5') then 'group2'
                     when max(lookupCurrentGroup('w_n_kuaishou_apps_did_4532', 0, device_id)) in ('exp6', 'exp7') then 'group3'
                     when max(lookupCurrentGroup('w_n_kuaishou_apps_did_4532', 0, device_id)) in ('exp8', 'exp9') then 'group4'
                    --  when max(lookupCurrentGroup('w_n_kuaishou_apps_did_3088', 0, device_id)) in ('exp5') then 'exp5'
                     else 'other'
                end as bucket_id
              , max(campaign_type) as campaign_type
              , max(item_type) as item_type
              , max(if(action_type = 'AD_DELIVERY',1,0)) as delivery
              , max(if(action_type = 'AD_PHOTO_IMPRESSION', 1, 0)) as photo_imp
              , max(if(action_type = 'AD_ITEM_IMPRESSION', 1, 0)) as item_imp
              , max(if(action_type = 'AD_PHOTO_CLICK',1,0)) as photo_clk
              , max(if(action_type = 'AD_ITEM_CLICK', 1, 0)) as item_clk
              , max(if(action_type = 'EVENT_FORM_SUBMIT', 1, 0)) as lps_clk
              , sum(if(action_type = 'AD_LIVE_PLAYED_STARTED' and live_room_pattern = 'STANDARD_LIVE_ROOM_PATTERN',1,0)) as slsp
              , max(cast(get_json_object(online_join_params, '$.predictUnifyCtr') as double)) as lps_ctr
              , max(cast(get_json_object(online_join_params, '$.predictUnifyCvr') as double)) as lps_cvr
              , max(cast(get_json_object(online_join_params, '$.serverShowCtr') as float)) as p_server_show_ctr             -- p_ad->get_server_show_ctr(), 单列
              , max(cast(get_json_object(online_join_params, '$.serverClientShowRate') as float)) as p_server_client_show_rate     -- p_ad->get_predict_score(PredictType::PredictType_server_client_show_rate)), 双列
              , max(cast(get_json_object(online_join_params, '$.adRankTransInfo.predictCxrPackage.predictFeedCtr') as double)) as feed_ctr  -- p_ad->get_ctr()
              , max(cast(get_json_object(online_join_params, '$.searchBidBoost') as double)) as bid_boost
       from ks_ad_dw.dwd_ad_tfc_adsearch_log_full_hi_view
       where p_date = '20250723'
              and is_for_report_engine = 1
              and ocpc_action_type in ('AD_LANDING_PAGE_FORM_SUBMITTED')
            --   and ocpc_action_type in ('AD_LANDING_PAGE_FORM_SUBMITTED', 'LEADS_SUBMIT', 'EVENT_WECHAT_CONNECTED', 'EVENT_PRIVATE_MESSAGE_SENT')
              and lookupCurrentExp('w_n_kuaishou_apps_did_4532', 0, device_id) = 'search_ads_did_rank_lps'
            --   and lookupCurrentGroup('w_n_kuaishou_apps_did_4532', 0, device_id) in ('base1', 'exp1', 'exp2', 'exp3', 'exp4', 'exp5', 'exp6', 'exp7', 'exp8', 'exp9')
              and get_json_object(search_info, '$.search_source') is not null
              and get_json_object(search_info, '$.search_source') not in ('', 'UNKOWN_SOURCE')
            --   and campaign_type in ('FANS_LIVE_STREAM_PROMOTE')
       group by p_date
              , llsid
              , creative_id
              , ocpc_action_type
       having delivery = 1
)

select p_date
       , ocpc_action_type
       , bucket_id
       , sum(if(item_imp=1, lps_cvr, 0))  as lps_prob_sum
       , sum(lps_clk) as lps_clk_sum
       -- sctr 下发空间
       , sum(case 
                when page_type = 'outer'  and style = 'normal' then photo_imp 
                when page_type = 'outer'  and style = 'bigcard' then item_imp 
                when page_type = 'inner' then item_imp
                else 0.0
             end) as sctr_real

        ,sum(case
                when page_type = 'outer'  and style = 'normal' then p_server_client_show_rate 
                when page_type = 'outer'  and style = 'bigcard' then p_server_client_show_rate --p_server_show_ctr 
                when page_type = 'inner'                        then p_server_show_ctr
                else 0.0
             end) as sctr_prob
        -- -- ctr 曝光空间（ctr只在双列生效）
        , sum(case
                when photo_imp = 1 and page_type = 'outer'  and style = 'normal' then photo_clk 
                else 0.0
              end
        ) as ctr_real
        ,sum(if(photo_imp = 1, feed_ctr, 0)) as ctr_prob
       , sum(if(item_imp = 1, lps_cvr, 0)) as lps_prob
       , sum(if(item_imp = 1, lps_clk, 0)) as lps_real
       , sum(case
                when page_type = 'outer'  and style = 'normal' then p_server_client_show_rate 
                when page_type = 'outer'  and style = 'bigcard' then p_server_client_show_rate --p_server_show_ctr 
                when page_type = 'inner'                        then p_server_show_ctr
                else 0.0
             end) * 1.000 / sum(case 
                when page_type = 'outer'  and style = 'normal' then photo_imp 
                when page_type = 'outer'  and style = 'bigcard' then item_imp 
                when page_type = 'inner' then item_imp
                else 0.0
             end) as sctr_pcoc
       , sum(if(photo_imp = 1, feed_ctr, 0)) * 1.000 / sum(case
                when photo_imp = 1 and page_type = 'outer'  and style = 'normal' then photo_clk 
                else 0.0
              end
        ) as ctr_pcoc
       , sum(if(item_imp = 1, lps_cvr, 0)) * 1.000 / sum(if(item_imp = 1, lps_clk, 0)) as lps_pcoc
       , avg(bid_boost) as bid_boost_avg
from lps_info
group by p_date
       , ocpc_action_type
       , bucket_id
